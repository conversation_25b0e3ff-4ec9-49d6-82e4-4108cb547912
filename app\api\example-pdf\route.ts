import { NextRequest, NextResponse } from 'next/server';
import { renderComponentToHtml } from '@/app/utils/renderComponent';
import { generatePdfFromHtml } from '@/app/utils/generatePdfFromHtml';
import Template_InspectionPdf from '@/templates/Template_InspectionPdf';
import { PropertyModel } from '@/app/models/Property';

/**
 * Example API route showing the direct server-side approach for PDF generation
 * This demonstrates using renderComponentToHtml directly within an API route
 */
export async function POST(request: NextRequest) {
    try {
        const { propertyData } = await request.json();

        if (!propertyData) {
            return NextResponse.json({ error: 'Property data is required' }, { status: 400 });
        }

        // Create PropertyModel instance
        const property = new PropertyModel(propertyData);

        // Method 1: Direct server-side rendering (recommended for API routes)
        const html = renderComponentToHtml(Template_InspectionPdf, property);

        // Generate PDF from HTML
        const pdfBuffer = await generatePdfFromHtml(html);

        // Return PDF as base64-encoded JSON (compatible with JC_Post)
        const base64Pdf = pdfBuffer.toString('base64');
        return NextResponse.json({
            pdfData: base64Pdf,
            filename: `example-report-${property.Id}.pdf`,
            contentType: 'application/pdf'
        }, { status: 200 });

    } catch (error) {
        console.error('Error generating example PDF:', error);
        return NextResponse.json({ error: 'Failed to generate PDF' }, { status: 500 });
    }
}

/**
 * Alternative approach using the render-html API route
 * This would be used if you need to render from client-side or server actions
 */
export async function GET(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const propertyId = params.get('propertyId');

        if (!propertyId) {
            return NextResponse.json({ error: 'Property ID is required' }, { status: 400 });
        }

        // Mock property data for example
        const mockProperty = new PropertyModel({
            Id: propertyId,
            Address: '123 Example Street, Example City',
            BuildingTypeCode: 'House',
            CompanyStrataTitleCode: 'Freehold'
        });

        // Method 2: Using the render-html API route (for demonstration)
        const renderResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/render-html`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                componentName: 'Template_InspectionPdf',
                props: mockProperty,
            }),
        });

        if (!renderResponse.ok) {
            throw new Error('Failed to render component');
        }

        const { html } = await renderResponse.json();

        // Return HTML for demonstration
        return new Response(html, {
            headers: { 'Content-Type': 'text/html' }
        });

    } catch (error) {
        console.error('Error in example route:', error);
        return NextResponse.json({ error: 'Failed to process request' }, { status: 500 });
    }
}
