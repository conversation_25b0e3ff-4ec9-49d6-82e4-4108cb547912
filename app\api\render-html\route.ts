import { NextRequest, NextResponse } from 'next/server';
import React from 'react';

// Component registry - map component names to actual components
import Template_InspectionPdf from '@/templates/Template_InspectionPdf';
import Template_ContactEmail from '@/templates/email/Contact';
import Template_WelcomeEmail from '@/templates/email/Welcome';
import Template_ResetPasswordEmail from '@/templates/email/ResetPassword';
import Template_UserVerificationEmail from '@/templates/email/UserVerification';

const componentRegistry = {
    'Template_InspectionPdf': Template_InspectionPdf,
    'Template_ContactEmail': Template_ContactEmail,
    'Template_WelcomeEmail': Template_WelcomeEmail,
    'Template_ResetPasswordEmail': Template_ResetPasswordEmail,
    'Template_UserVerificationEmail': Template_UserVerificationEmail,
    // Add more components as needed
} as const;

export async function POST(request: NextRequest) {
    try {
        const { componentName, props } = await request.json();

        if (!componentName) {
            return NextResponse.json({ error: 'Component name is required' }, { status: 400 });
        }

        // Get component from registry
        const Component = componentRegistry[componentName as keyof typeof componentRegistry];

        if (!Component) {
            return NextResponse.json({
                error: `Component '${componentName}' not found. Available components: ${Object.keys(componentRegistry).join(', ')}`
            }, { status: 404 });
        }

        // Dynamic import of react-dom/server to avoid build issues
        const { renderToStaticMarkup } = await import('react-dom/server');

        // Render component to HTML
        const element = React.createElement(Component as any, props || {});
        const html = `<!DOCTYPE html>${renderToStaticMarkup(element)}`;

        return NextResponse.json({ html }, { status: 200 });
    } catch (error) {
        console.error('Error rendering component:', error);
        return NextResponse.json({ error: 'Failed to render component' }, { status: 500 });
    }
}
