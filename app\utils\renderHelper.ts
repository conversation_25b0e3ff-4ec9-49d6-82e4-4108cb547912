/**
 * Client-side helper for rendering React components to HTML
 * This function calls the render-html API route to render components server-side
 */
export async function renderComponentToHtml<P>(
    componentName: string,
    props: P
): Promise<string> {
    const response = await fetch('/api/render-html', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            componentName,
            props,
        }),
    });
    
    if (!response.ok) {
        throw new Error(`Failed to render component: ${response.statusText}`);
    }
    
    const { html } = await response.json();
    return html;
}
