import 'server-only';
import React from 'react';
import { renderToStaticMarkup } from 'react-dom/server';

/**
 * Server-side function to render React components to HTML
 * This function should only be used in server-side contexts (API routes, server actions)
 * @param Component The React component to render
 * @param props The props to pass to the component
 * @returns HTML string with DOCTYPE
 */
export function renderComponentToHtml<P extends Record<string, any> = any>(
    Component: React.ComponentType<P>,
    props: P
): string {
    try {
        // Create React element with the component and props
        const element = React.createElement(Component as any, props as any);

        // Render to static HTML markup
        const html = renderToStaticMarkup(element);

        // Return with DOCTYPE declaration
        return `<!DOCTYPE html>${html}`;
    } catch (error) {
        console.error('Error rendering component to HTML:', error);
        throw error;
    }
}
