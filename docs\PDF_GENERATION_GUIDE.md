# PDF Generation Guide

This guide explains how to use the new PDF generation system that follows the recommended patterns for React component rendering and PDF generation.

## Overview

The system provides multiple approaches for rendering React components to HTML and generating PDFs:

1. **Direct Server-Side Rendering** (recommended for API routes)
2. **Client-Side Helper** (for client components)
3. **Server Actions** (for server components and form actions)

## File Structure

```
app/
├── utils/
│   ├── renderComponent.ts      # Direct server-side rendering
│   ├── renderHelper.ts         # Client-side helper
│   ├── Utils_Server.ts         # Server utilities wrapper
│   └── generatePdfFromHtml.ts  # PDF generation utility
├── api/
│   ├── render-html/
│   │   └── route.ts           # Generic HTML rendering API
│   └── pdf/
│       └── generateInspectionReport/
│           └── route.ts       # Specific PDF generation API
└── actions/
    └── generateReport.ts      # Example server action
```

## Usage Patterns

### 1. Direct Server-Side Rendering (API Routes)

**Best for:** API routes where you have direct access to components

```typescript
// app/api/my-pdf/route.ts
import { renderComponentToHtml } from '@/app/utils/renderComponent';
import { generatePdfFromHtml } from '@/app/utils/generatePdfFromHtml';
import MyComponent from '@/templates/MyComponent';

export async function POST(request: NextRequest) {
    const data = await request.json();
    
    // Render component directly
    const html = renderComponentToHtml(MyComponent, data);
    
    // Generate PDF
    const pdfBuffer = await generatePdfFromHtml(html);
    
    return NextResponse.json({
        pdfData: pdfBuffer.toString('base64'),
        filename: 'my-document.pdf'
    });
}
```

### 2. Client-Side Helper

**Best for:** Client components that need to generate PDFs

```typescript
// In a client component
import { renderComponentToHtml } from '@/app/utils/renderHelper';

const handleGeneratePDF = async () => {
    try {
        // Render component via API
        const html = await renderComponentToHtml('Template_InspectionPdf', data);
        
        // Use the HTML as needed (send to PDF API, etc.)
        const response = await fetch('/api/generate-pdf', {
            method: 'POST',
            body: JSON.stringify({ html })
        });
    } catch (error) {
        console.error('Failed to generate PDF:', error);
    }
};
```

### 3. Server Actions

**Best for:** Server components and form actions

```typescript
// app/actions/generateReport.ts
'use server';

export async function generateReportHtml(reportData: any) {
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/render-html`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            componentName: 'Template_InspectionPdf',
            props: reportData,
        }),
    });
    
    const { html } = await response.json();
    return html;
}
```

## Component Registry

To use the `/api/render-html` endpoint, components must be registered in the component registry:

```typescript
// app/api/render-html/route.ts
const componentRegistry = {
    'Template_InspectionPdf': Template_InspectionPdf,
    'Template_ContactEmail': Template_ContactEmail,
    // Add your components here
};
```

## Best Practices

1. **Use Direct Rendering for API Routes**: When building API routes, use `renderComponentToHtml` directly for better performance
2. **Use Client Helper for Interactive Features**: When users need to generate PDFs from the UI
3. **Register Components**: Add new template components to the registry for API access
4. **Error Handling**: Always wrap rendering calls in try-catch blocks
5. **Environment Variables**: Set `NEXT_PUBLIC_APP_URL` for server actions

## Migration from Old System

The old `serverActions/renderComponentToHtml.server.ts` is deprecated. Update imports:

```typescript
// Old (deprecated)
import { renderComponentToHtml } from '../../../../serverActions/renderComponentToHtml.server';

// New (recommended)
import { renderComponentToHtml } from '@/app/utils/renderComponent';
```

## Examples

See the following files for complete examples:
- `app/api/pdf/generateInspectionReport/route.ts` - Updated PDF generation
- `app/api/example-pdf/route.ts` - Example showing both approaches
- `app/actions/generateReport.ts` - Server action example
