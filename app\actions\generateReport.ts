'use server';

/**
 * Example server action showing how to use the new render-html API
 * This demonstrates the pattern for generating HTML from React components in server actions
 */
export async function generateReportHtml(reportData: any) {
    try {
        // Call the API route internally using the full URL
        const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/render-html`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                componentName: 'Template_InspectionPdf',
                props: reportData,
            }),
        });

        if (!response.ok) {
            throw new Error(`Failed to render component: ${response.statusText}`);
        }

        const { html } = await response.json();
        return html;
    } catch (error) {
        console.error('Error generating report HTML:', error);
        throw error;
    }
}

/**
 * Alternative approach: Direct function call for server-side usage
 * Use this when calling from another API route where you have direct access to the components
 */
export async function generateReportHtmlDirect(reportData: any) {
    // This would be used in API routes where you can import the renderComponent function directly
    // See app/utils/renderComponent.ts for the direct approach
    throw new Error('Use renderComponentToHtml from app/utils/renderComponent.ts for direct server-side rendering');
}
